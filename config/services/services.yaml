services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    Cdn77\NxgApi\Resource\Domain\ResourceSuspender:
        public: true

    Cdn77\NxgApi\Server\Domain\ServerPauser:
        public: true

    Cdn77\NxgApi\Core\Application\OpenApi\OpenApiSpec:
        arguments:
            $scheme: '%env(OPEN_API_SPEC_SCHEME)%'
            $host: '%env(OPEN_API_SPEC_HOST)%'

    Cdn77\NxgApi\Core\Application\Serializer\StrictBooleanHandler:
        tags:
            - { name: jms_serializer.handler, type: 'boolStrict', format: 'json' }

    Cdn77\NxgApi\Core\Application\Serializer\StrictStringHandler:
        tags:
            - { name: jms_serializer.handler, type: 'stringStrict', format: 'json' }

    Cdn77\NxgApi\CanaryCheck\Domain\Comparator:
        $urlCanary: '%env(CANARY_URL_CANARY)%'
        $urlProduction: '%env(CANARY_URL_PRODUCTION)%'

    Metadata\MetadataFactoryInterface:
        alias: jms_serializer.metadata_factory

    JMS\Serializer\Naming\PropertyNamingStrategyInterface:
        alias: jms_serializer.camel_case_naming_strategy

    JMS\Serializer\SerializerInterface:
        alias: 'jms_serializer.serializer'

    Cdn77\NxgApi\Certificate\Domain\CertificateCleaner:
        $coldFilesystem: '@League\Flysystem\FilesystemOperator.certificate_cold'
        $bucketFilesystem: '@League\Flysystem\FilesystemOperator.certificate_bucket'
        $pathBucket: '%env(CERTIFICATES_PATH)%'
        $pathCold: '%env(CERTIFICATES_COLD_PATH)%'

    Cdn77\NxgApi\Resource\Infrastructure\Finder\FilesystemMainCertificateFinder:
        $filesystem: '@League\Flysystem\FilesystemOperator.certificate_bucket'
        
    Cdn77\NxgApi\Certificate\Domain\Command\LoadCertificatesHandler:
        $certificateStorage: '@League\Flysystem\FilesystemOperator.certificate_storage'

    Cdn77\NxgApi\Certificate\Domain\Command\StoreCertificateHandler:
        $certificateStorage: '@League\Flysystem\FilesystemOperator.certificate_storage'
        
    Cdn77\NxgApi\Certificate\Domain\Command\DeleteCertificateHandler:
        $certificateStorage: '@League\Flysystem\FilesystemOperator.certificate_storage'

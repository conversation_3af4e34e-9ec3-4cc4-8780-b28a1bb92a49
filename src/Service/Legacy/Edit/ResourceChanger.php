<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Service\Legacy\Edit;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam;
use Cdn77\NxgApi\Entity\Legacy\LocationGroup;
use Cdn77\NxgApi\Entity\Legacy\ResourceCaching;
use Cdn77\NxgApi\Entity\Legacy\SecureToken;
use Cdn77\NxgApi\Resource\Domain\DTO\EditedResource;
use Cdn77\NxgApi\Resource\Domain\Repository\SecureTokenRepository;
use Cdn77\NxgApi\Resource\Domain\ResourceOriginCreator;
use Cdn77\NxgApi\Service\Event\ResourceChangedEvent;
use Cdn77\NxgApi\Service\Legacy\Account\AccountManager;
use Cdn77\NxgApi\Service\Legacy\Edit\Exception\ResourceValidationFailed;
use Cdn77\NxgApi\Service\Legacy\Locations\Exception\NoCustomLocationExists;
use Cdn77\NxgApi\Service\Legacy\Locations\ResourceLocationsChanger;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Webmozart\Assert\Assert;

use function assert;
use function in_array;

class ResourceChanger
{
    public const DISABLE_SECURE_TOKEN_TYPE_STRING = 'none';

    private AccountManager $accountManager;

    private ResourceLocationsChanger $resourceLocationsChanger;

    private EntityManagerInterface $entityManager;

    private EventDispatcherInterface $eventDispatcher;

    private ValidatorInterface $validator;
    private SecureTokenRepository $secureTokenRepository;
    private ResourceOriginCreator $resourceOriginCreator;

    public function __construct(
        AccountManager $accountManager,
        ResourceLocationsChanger $resourceLocationsChanger,
        EntityManagerInterface $entityManager,
        EventDispatcherInterface $eventDispatcher,
        ValidatorInterface $validator,
        SecureTokenRepository $secureTokenRepository,
        ResourceOriginCreator $resourceOriginCreator,
    ) {
        $this->accountManager = $accountManager;
        $this->resourceLocationsChanger = $resourceLocationsChanger;
        $this->entityManager = $entityManager;
        $this->eventDispatcher = $eventDispatcher;
        $this->validator = $validator;
        $this->secureTokenRepository = $secureTokenRepository;
        $this->resourceOriginCreator = $resourceOriginCreator;
    }

    public function modify(CdnResource $resource, EditedResource $editedResource): CdnResource
    {
        $originalResource = clone $resource;

        $isInLsGroup = in_array($resource->getGroup()->getId(), ResourceCaching::LS_RESOURCE_CACHING_GROUPS, true);
        $isMovedToLsGroup = in_array(
            $editedResource->groupId,
            ResourceCaching::LS_RESOURCE_CACHING_GROUPS,
            true,
        );

        $this->doModify($resource, $editedResource);
        $resource->modifyResource($editedResource);

        $this->validateResource($resource);
        $this->handleGroupChange($originalResource, $resource);

        if ($isMovedToLsGroup === true) {
            $this->setLsCacheLockAgeAndLockTimeout($resource);
        }

        if ($isInLsGroup && $isMovedToLsGroup === false && $editedResource->groupId !== null) {
            $this->removeLsCacheLockAgeAndLockTimeout($resource);
        }

        $this->resourceOriginCreator->createFromEditedResource($resource, $editedResource);

        $resource->setUpdated(new DateTimeImmutable());
        $this->eventDispatcher->dispatch(new ResourceChangedEvent($originalResource, $resource));

        return $resource;
    }

    private function doModify(CdnResource $resource, EditedResource $editedResource): void
    {
        $this->modifyAccount($resource, $editedResource);
        $this->modifyGroup($resource, $editedResource);
        $this->modifyCnames($resource, $editedResource);
        $this->modifyCacheExpiry($resource, $editedResource);
        $this->modifyCacheExpiry404($resource, $editedResource);
        $this->modifyIgnoredQueryParams($resource, $editedResource);
        $this->modifyCustomData($resource, $editedResource);
        $this->modifySecureToken($resource, $editedResource);

        $resource->setResponseHeaders($editedResource->responseHeaders);
    }

    private function modifyAccount(CdnResource $resource, EditedResource $editedResource): void
    {
        if ($editedResource->accountId === null) {
            return;
        }

        $this->handleAccountChange($resource, $editedResource->accountId);
    }

    private function modifyGroup(CdnResource $resource, EditedResource $editedResource): void
    {
        if ($editedResource->groupId === null) {
            return;
        }

        $group = $this->entityManager->getRepository(LocationGroup::class)->find($editedResource->groupId);
        assert($group instanceof LocationGroup);
        $resource->setGroup($group);
    }

    private function modifyCnames(CdnResource $resource, EditedResource $editedResource): void
    {
        if ($editedResource->cnames === null) {
            return;
        }

        $resource->setCnames($editedResource->cnames);
    }

    private function modifyCacheExpiry(CdnResource $resource, EditedResource $editedResource): void
    {
        if ($editedResource->cacheExpiry === null) {
            return;
        }

        $resource->setCaching($resource->getCaching()->withExpiry($editedResource->cacheExpiry));
    }

    private function modifyCacheExpiry404(CdnResource $resource, EditedResource $editedResource): void
    {
        $resource->setCaching($resource->getCaching()->withExpiry404($editedResource->cacheExpiry404));
    }

    private function modifyIgnoredQueryParams(CdnResource $resource, EditedResource $editedResource): void
    {
        if ($editedResource->ignoredQueryParams === null) {
            return;
        }

        foreach ($editedResource->ignoredQueryParams as $param) {
            if (isset($resource->getIgnoredQueryParams()[$param])) {
                continue;
            }

            $resource->getIgnoredQueryParams()[$param] = new IgnoredQueryParam($resource, $param);
        }

        foreach ($resource->getIgnoredQueryParams() as $param) {
            if (in_array($param->getName(), $editedResource->ignoredQueryParams, true)) {
                continue;
            }

            $resource->getIgnoredQueryParams()->removeElement($param);
            $this->entityManager->remove($param);
        }
    }

    private function modifyCustomData(CdnResource $resource, EditedResource $editedResource): void
    {
        if ($editedResource->customData === null) {
            return;
        }

        $resource->setCustomData($editedResource->customData);
    }

    private function setLsCacheLockAgeAndLockTimeout(CdnResource $resource): void
    {
        $resourceCaching = $resource->getCaching();

        $resourceCaching->setLivestreamCacheAgeAndCacheLockout();
        $resource->setCaching($resourceCaching);
    }

    private function removeLsCacheLockAgeAndLockTimeout(CdnResource $resource): void
    {
        $resourceCaching = $resource->getCaching();

        $resourceCaching->setDefaultCacheAgeAndCacheLockout();
        $resource->setCaching($resourceCaching);
    }

    private function validateResource(CdnResource $resource): void
    {
        $violations = $this->validator->validate(
            $resource,
            [new Constraints\UniqueCname()],
        );

        if ($violations->count() === 0) {
            return;
        }

        throw new ResourceValidationFailed($violations);
    }

    private function handleGroupChange(CdnResource $original, CdnResource $updated): void
    {
        if ($updated->getGroup() === $original->getGroup()) {
            // group not changed
            return;
        }

        try {
            $this->resourceLocationsChanger->resetCustomLocations($updated);
        } catch (NoCustomLocationExists) {
            // ignore
        }
    }

    private function handleAccountChange(CdnResource $resource, int $accountId): void
    {
        $account = $this->accountManager->find($accountId);

        if ($account === null) {
            $account = $this->accountManager->create($accountId);
        }

        if ($resource->getAccount() === $account) {
            return;
        }

        $resource->getAccount()->getResources()->removeElement($resource);
        $resource->setAccount($account);

        $account->getResources()->add($resource);
    }

    private function modifySecureToken(CdnResource $resource, EditedResource $editedResource): void
    {
        if ($editedResource->secureTokenSchema === null) {
            return;
        }

        $secureToken = $this->secureTokenRepository->findForResource($resource);

        if ($editedResource->secureTokenSchema->secureTokenType === self::DISABLE_SECURE_TOKEN_TYPE_STRING) {
            if ($secureToken !== null) {
                $this->secureTokenRepository->remove($secureToken);
                $resource->setResourceSecureToken(null);
            }

            return;
        }

        Assert::string($editedResource->secureTokenSchema->secureTokenType);
        Assert::string($editedResource->secureTokenSchema->secureTokenValue);

        if ($secureToken === null) {
            $secureToken = new SecureToken(
                $resource,
                $editedResource->secureTokenSchema->secureTokenType,
                $editedResource->secureTokenSchema->secureTokenValue,
                ValueReplacer::emptyStringToNull($editedResource->secureTokenSchema->secureLinkExpiryParam),
                ValueReplacer::emptyStringToNull($editedResource->secureTokenSchema->secureLinkTokenParam),
                ValueReplacer::emptyStringToNull($editedResource->secureTokenSchema->secureLinkPathlenParam),
                ValueReplacer::emptyStringToNull($editedResource->secureTokenSchema->secureLinkSecretParam),
                $editedResource->secureTokenSchema->secureLinkRewritePlaylist,
            );

            $this->secureTokenRepository->add($secureToken);
        } else {
            $secureToken->setType($editedResource->secureTokenSchema->secureTokenType);
            $secureToken->setValue($editedResource->secureTokenSchema->secureTokenValue);
            $secureToken->setSecureLinkExpiryParam(
                ValueReplacer::emptyStringToNull($editedResource->secureTokenSchema->secureLinkExpiryParam),
            );
            $secureToken->setSecureLinkTokenParam(
                ValueReplacer::emptyStringToNull($editedResource->secureTokenSchema->secureLinkTokenParam),
            );
            $secureToken->setSecureLinkPathlenParam(
                ValueReplacer::emptyStringToNull($editedResource->secureTokenSchema->secureLinkPathlenParam),
            );
            $secureToken->setSecureLinkSecretParam(
                ValueReplacer::emptyStringToNull($editedResource->secureTokenSchema->secureLinkSecretParam),
            );
            $secureToken->setSecureLinkRewritePlaylist($editedResource->secureTokenSchema->secureLinkRewritePlaylist);
        }

        $resource->setResourceSecureToken($secureToken);
    }
}

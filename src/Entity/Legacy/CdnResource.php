<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Entity\Legacy;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Entity\Legacy\Id\ResourceIdGenerator;
use Cdn77\NxgApi\Repository\Legacy\CdnResourceRepository;
use Cdn77\NxgApi\Resource\Domain\DTO\EditedResource;
use Cdn77\NxgApi\Resource\Domain\DTO\NewResource;
use Cdn77\NxgApi\Resource\Domain\DTO\ResponseHeaders;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\OneToOne;
use Webmozart\Assert\Assert;

use function array_filter;
use function array_map;
use function array_merge;
use function array_unique;
use function array_values;
use function assert;
use function count;
use function implode;
use function preg_quote;
use function Safe\preg_match;

/**
 * @ORM\Entity(repositoryClass=CdnResourceRepository::class)
 * @ORM\Table(name="resource")
 * @ORM\HasLifecycleCallbacks
 */
class CdnResource
{
    public const CDN_URL_SUFFIX = '.rsc.cdn77.org';

    public const SHARED_SSL_URL_SUFFIX = '.c.cdn77.org';
    public const LEGACY_URL_SUFFIX = '.r.cdn77.net';
    public const LEGACY_SSL_URL_SUFFIX = '.cdn77-ssl.net';

    public const CNAME_URL_SUFFIXES = [
        self::SHARED_SSL_URL_SUFFIX,
        self::LEGACY_URL_SUFFIX,
        self::LEGACY_SSL_URL_SUFFIX,
    ];

    // https://regex101.com/r/NFStvw/1
    public const PATTERN_CDN77_CEPH_RGW_ORIGIN_HOST = '~[a-z]+\-\d+\.cdn77-storage\.com~';

    public const SECURE_TOKEN_TYPE_PARAMETER = 'parameter';
    public const SECURE_TOKEN_TYPE_PATH = 'path';
    public const SECURE_TOKEN_TYPE_HIGHWINDS = 'highwinds';
    public const SECURE_TOKEN_TYPES = [
        self::SECURE_TOKEN_TYPE_PARAMETER,
        self::SECURE_TOKEN_TYPE_PATH,
        self::SECURE_TOKEN_TYPE_HIGHWINDS,
    ];

    public const COLUMN_ACCOUNT = 'account_id';
    public const COLUMN_CACHE_BYPASS = 'cache_bypass';
    public const COLUMN_CACHE_CONTENT_LENGHT_LIMIT = 'cache_content_length_limit';
    public const COLUMN_CACHE_EXPIRY = 'cache_expiry';
    public const COLUMN_CACHE_EXPIRY_404 = 'cache_expiry_404';
    public const COLUMN_CACHE_LOCK_AGE = 'cache_lock_age';
    public const COLUMN_CACHE_LOCK_TIMEOUT = 'cache_lock_timeout';
    public const COLUMN_CACHE_MIN_USES = 'cache_min_uses';
    public const COLUMN_CACHE_MISSING_CONTENT_LENGHT_LIMIT = 'cache_missing_content_length_limit';
    public const COLUMN_CDN_URL = 'cdn_url';
    public const COLUMN_CNAMES = 'cnames';
    public const COLUMN_CONTENT_DISPOSITION_BY_PARAM = 'content_disposition_by_param';
    public const COLUMN_CORS_ORIGIN_HEADER = 'cors_origin_header';
    public const COLUMN_CORS_TIMING_ENABLED = 'cors_timing_enabled';
    public const COLUMN_CORS_WILDCARD_ENABLED = 'cors_wildcard_enabled';
    public const COLUMN_CREATED = 'created';
    public const COLUMN_CUSTOM_DATA = 'custom_data';
    public const COLUMN_DELETED = 'deleted';
    public const COLUMN_DISABLE_QUERY_STRING = 'disable_query_string';
    public const COLUMN_FLV_PSEUDO_STREAMING = 'flv_pseudo_streaming';
    public const COLUMN_GROUP_ID = 'group_id';
    public const COLUMN_GROUP_ID_UPDATED = 'group_id_updated';
    public const COLUMN_HTTPS_REDIRECT_CODE = 'https_redirect_code';
    public const COLUMN_ID = 'id';
    public const COLUMN_IGNORE_SET_COOKIE = 'ignore_set_cookie';
    public const COLUMN_INSTANT_SSL = 'instant_ssl';
    public const COLUMN_MP4_PSEUDO_STREAMING = 'mp4_pseudo_streaming';
    public const COLUMN_PURGE_ALL_KEY = 'purge_all_key';
    public const COLUMN_QUIC = 'quic';
    public const COLUMN_RATE_LIMIT = 'rate_limit';
    public const COLUMN_STREAMING_PLAYLIST_BYPASS = 'streaming_playlist_bypass';
    public const COLUMN_SUSPENDED = 'suspended';
    public const COLUMN_UPDATED = 'updated';
    public const COLUMN_UPSTREAM_FAIL_TIMEOUT = 'upstream_fail_timeout';
    public const COLUMN_UPSTREAM_NEXT_ATTEMPTS = 'upstream_next_attempts';
    public const COLUMN_WAF = 'waf';

    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue("CUSTOM")
     * @ORM\CustomIdGenerator(ResourceIdGenerator::class)
     *
     * @var int|null
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Account::class, inversedBy="resources")
     * @ORM\JoinColumn(name="account_id", referencedColumnName="id", nullable=false)
     */
    private Account $account;

    /**
     * @ORM\OneToMany(
     *     targetEntity="Cdn77\NxgApi\Entity\Legacy\ResourceOrigin",
     *     mappedBy="resource",
     *     cascade={"all"},
     *     fetch="EXTRA_LAZY",
     *     indexBy="priority"
     * )
     * @ORM\OrderBy({"priority" = "ASC"})
     *
     * @var Collection<int, ResourceOrigin>
     */
    private Collection $origins;

    /**
     * @ORM\Column(name="cdn_url", type="string")
     *
     * @var string
     */
    private $cdnUrl;

    /**
     * @ORM\Column(name="cnames", type="resource_cnames_array")
     *
     * @var string[]
     */
    private $cnames = [];

    /**
     * @ORM\OneToOne(targetEntity=LocationGroup::class)
     * @ORM\JoinColumn(name="group_id", referencedColumnName="id", nullable=false)
     *
     * @var LocationGroup
     */
    private $group;

    /**
     * @ORM\Column(name="group_id_updated", type="datetimetz_immutable", nullable=true)
     *
     * @var DateTimeImmutable|null
     */
    private $groupUpdated;

    /**
     * @ORM\Column(name="purge_all_key", type="integer")
     *
     * @var int
     */
    private $purgeAllKey = 0;

    /**
     * @ORM\Column(name="disable_query_string", type="boolean")
     *
     * @var bool
     */
    private $disableQueryString = false;

    /**
     * @ORM\Column(name="mp4_pseudo_streaming", type="boolean")
     *
     * @var bool
     */
    private $mp4PseudoStreaming = false;

    /**
     * @ORM\Column(name="flv_pseudo_streaming", type="boolean")
     *
     * @var bool
     */
    private $flvPseudoStreaming = false;

    /**
     * @ORM\Column(name="ignore_set_cookie", type="boolean")
     *
     * @var bool
     */
    private $ignoreSetCookie = false;

    /**
     * @ORM\Column(name="created", type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $created;

    /**
     * @ORM\Column(name="updated", type="datetimetz_immutable")
     *
     * @var DateTimeImmutable
     */
    private $updated;

    /**
     * @ORM\Column(name="suspended", type="datetimetz_immutable", nullable=true)
     *
     * @var DateTimeImmutable|null
     */
    private $suspended;

    /**
     * @ORM\Column(name="deleted", type="datetimetz_immutable", nullable=true)
     *
     * @var DateTimeImmutable|null
     */
    private $deleted;

    /**
     * @ORM\Column(name="instant_ssl", type="boolean")
     *
     * @var bool
     */
    private $instantSsl = false;

    /**
     * @ORM\OneToMany(
     *     targetEntity="Cdn77\NxgApi\Entity\Legacy\IgnoredQueryParam",
     *     mappedBy="resource",
     *     cascade={"all"},
     *     fetch="EXTRA_LAZY",
     *     indexBy="name"
     * )
     *
     * @var Collection<string, IgnoredQueryParam>
     */
    private $ignoredQueryParams;

    /**
     * @ORM\Column(name="https_redirect_code", type="integer", nullable=true)
     *
     * @var int|null
     */
    private $httpsRedirectCode;

    /**
     * @ORM\Column(name="streaming_playlist_bypass", type="boolean")
     *
     * @var bool
     */
    private $streamingPlaylistBypass = false;

    /**
     * @ORM\Embedded(class=ResourceCaching::class, columnPrefix="cache_")
     *
     * @var ResourceCaching
     */
    private $caching;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @var int|null
     */
    private $upstreamFailTimeout;

    /**
     * @ORM\Column(type="integer", nullable=true)
     *
     * @var int|null
     */
    private $upstreamNextAttempts;

    /**
     * @ORM\Column(type="json", nullable=true)
     *
     * @var mixed[]|null
     */
    private $customData;

    /**
     * @ORM\Column(name="quic", type="boolean")
     *
     * @var bool
     */
    private $quic = false;

    /**
     * @ORM\Column(name="waf", type="boolean")
     *
     * @var bool
     */
    private $waf = false;

    /**
     * @ORM\Column(name="cors_origin_header", type="boolean")
     *
     * @var bool
     */
    private $corsOriginHeader = false;

    /** @ORM\Column(name="cors_timing_enabled", type="boolean") */
    private bool $corsTimingEnabled = false;

    /** @ORM\Column(name="cors_wildcard_enabled", type="boolean") */
    private bool $corsWildcardEnabled = false;

    /** @ORM\Column(name="content_disposition_by_param", type="boolean") */
    private bool $contentDispositionByParam = false;

    /** @ORM\Column(name="rate_limit", type="boolean") */
    private bool $rateLimit = false;

    /** @OneToOne(targetEntity="SecureToken", mappedBy="resource") */
    private SecureToken|null $resourceSecureToken = null;

    /**
     * @ORM\Column(name="response_headers", type="json", nullable=true, options={"jsonb"=true})
     *
     * @var array<string, string>|null
     */
    private array|null $responseHeaders = null;

    public function __construct(
        DateTimeImmutable $inceptionTime,
        int|null $id = null,
        string|null $cdnUrl = null,
        bool $disableQueryString = false,
        bool $mp4PseudoStreaming = false,
        bool $flvPseudoStreaming = false,
        int|null $httpsRedirectCode = null,
        bool $ignoreSetCookie = false,
        bool $instantSsl = false,
        bool $streamingPlaylistBypass = false,
        bool $quic = false,
        bool $waf = false,
        bool $corsOriginHeader = false,
        bool $corsTimingEnabled = false,
        bool $corsWildcardEnabled = false,
        bool $rateLimit = false,
        bool $contentDispositionByParam = false,
        ResponseHeaders|null $responseHeaders = null,
    ) {
        $this->id = $id;
        if ($cdnUrl !== null) {
            $this->cdnUrl = $cdnUrl;
        }

        $this->caching = new ResourceCaching();
        $this->corsOriginHeader = $corsOriginHeader;
        $this->corsTimingEnabled = $corsTimingEnabled;
        $this->corsWildcardEnabled = $corsWildcardEnabled;
        $this->created = $inceptionTime;
        $this->disableQueryString = $disableQueryString;
        $this->flvPseudoStreaming = $flvPseudoStreaming;
        $this->groupUpdated = $inceptionTime;
        $this->httpsRedirectCode = $httpsRedirectCode;
        $this->ignoredQueryParams = new ArrayCollection();
        $this->origins = new ArrayCollection();
        $this->ignoreSetCookie = $ignoreSetCookie;
        $this->instantSsl = $instantSsl;
        $this->mp4PseudoStreaming = $mp4PseudoStreaming;
        $this->quic = $quic;
        $this->streamingPlaylistBypass = $streamingPlaylistBypass;
        $this->updated = $inceptionTime;
        $this->waf = $waf;
        $this->rateLimit = $rateLimit;
        $this->contentDispositionByParam = $contentDispositionByParam;
        $this->setResponseHeaders($responseHeaders);
    }

    public static function fromNewResource(DateTimeImmutable $now, NewResource $newResource): self
    {
        return new self(
            $now,
            $newResource->id,
            $newResource->cdnUrl,
            $newResource->disableQueryString,
            $newResource->mp4PseudoStreaming,
            false,
            $newResource->httpsRedirectCode === null || $newResource->httpsRedirectCode === 0
                ? null
                : $newResource->httpsRedirectCode,
            $newResource->ignoreSetCookie,
            $newResource->instantSsl,
            $newResource->streamingPlaylistBypass ?? false,
            $newResource->quic,
            $newResource->waf,
            $newResource->corsOriginHeader,
            false,
            false,
            $newResource->rateLimit ?? false,
            $newResource->contentDispositionByParam ?? false,
            $newResource->responseHeaders,
        );
    }

    public function getId(): int
    {
        assert($this->id !== null);

        return $this->id;
    }

    public function getResourceId(): int|null
    {
        return $this->id ?? null;
    }

    public function getAccount(): Account
    {
        return $this->account;
    }

    public function setAccount(Account $account): void
    {
        $this->account = $account;
    }

    /** @return Collection<int, ResourceOrigin> */
    public function getOrigins(): Collection
    {
        return $this->origins;
    }

    public function getMainOrigin(): ResourceOrigin
    {
        $origin = $this->origins->first();
        Assert::isInstanceOf($origin, ResourceOrigin::class);

        return $origin;
    }

    public function getSecondOrigin(): ResourceOrigin
    {
        $origin = $this->origins->get(2);
        Assert::isInstanceOf($origin, ResourceOrigin::class);

        return $origin;
    }

    public function addOrigin(ResourceOrigin $origin): void
    {
        $this->origins->add($origin);
    }

    public function getCdnUrl(): string
    {
        return $this->cdnUrl;
    }

    public function setCdnUrl(string $cdnUrl): void
    {
        $this->cdnUrl = $cdnUrl;
    }

    /**
     * @internal
     *
     * @ORM\PreFlush
     */
    public function setDefaultCdnUrl(): void
    {
        if ($this->cdnUrl !== null) {
            return;
        }

        assert($this->id !== null);
        $this->cdnUrl = $this->id . self::CDN_URL_SUFFIX;
    }

    /** @return string[] */
    public function getCnames(): array
    {
        return $this->cnames;
    }

    /** @param string[] $cnames */
    public function setCnames(array $cnames): void
    {
        $cnames = array_map('trim', $cnames);
        $cnames = array_map('mb_strtolower', $cnames);
        // TODO maybe raise exception if non-unique cnames are set?
        $this->cnames = array_unique($cnames);
    }

    public function addCname(string $cname): void
    {
        $this->setCnames(array_merge($this->getCnames(), [$cname]));
    }

    /** @return string[] */
    public function getCustomCnames(): array
    {
        return $this->getSharedOrCustomCnames(false);
    }

    /** @return string[] */
    public function getSharedCnames(): array
    {
        return $this->getSharedOrCustomCnames(true);
    }

    public function hasCustomCnames(): bool
    {
        return count($this->getCustomCnames()) > 0;
    }

    public function hasSharedCnames(): bool
    {
        return count($this->getSharedCnames()) > 0;
    }

    public function getGroup(): LocationGroup
    {
        return $this->group;
    }

    public function setGroup(LocationGroup $group): void
    {
        $this->group = $group;
    }

    public function getGroupUpdated(): DateTimeImmutable|null
    {
        return $this->groupUpdated;
    }

    public function setGroupUpdated(DateTimeImmutable|null $groupUpdated): void
    {
        $this->groupUpdated = $groupUpdated;
    }

    public function getPurgeAllKey(): int
    {
        return $this->purgeAllKey;
    }

    public function setPurgeAllKey(int $purgeAllKey): void
    {
        $this->purgeAllKey = $purgeAllKey;
    }

    public function increasePurgeAllKey(): void
    {
        $this->purgeAllKey++;
    }

    public function isDisableQueryString(): bool
    {
        return $this->disableQueryString;
    }

    public function isMp4PseudoStreaming(): bool
    {
        return $this->mp4PseudoStreaming;
    }

    public function isFlvPseudoStreaming(): bool
    {
        return $this->flvPseudoStreaming;
    }

    public function setFlvPseudoStreaming(bool $flvPseudoStreaming): void
    {
        $this->flvPseudoStreaming = $flvPseudoStreaming;
    }

    public function isIgnoreSetCookie(): bool
    {
        return $this->ignoreSetCookie;
    }

    public function getCreated(): DateTimeImmutable
    {
        return $this->created;
    }

    public function getUpdated(): DateTimeImmutable
    {
        return $this->updated;
    }

    public function setUpdated(DateTimeImmutable $updated): void
    {
        $this->updated = $updated;
    }

    public function getSuspended(): DateTimeImmutable|null
    {
        return $this->suspended;
    }

    public function setSuspended(DateTimeImmutable|null $suspended): void
    {
        $this->suspended = $suspended;
    }

    public function isSuspended(): bool
    {
        return $this->suspended !== null;
    }

    public function getDeleted(): DateTimeImmutable|null
    {
        return $this->deleted;
    }

    public function setDeleted(DateTimeImmutable $deleted): void
    {
        $this->deleted = $deleted;
    }

    public function isDeleted(): bool
    {
        return $this->deleted !== null;
    }

    public function hasInstantSsl(): bool
    {
        return $this->instantSsl;
    }

    public function setInstantSsl(bool $instantSsl): void
    {
        $this->instantSsl = $instantSsl;
    }

    /** @return Collection<string, IgnoredQueryParam> */
    public function getIgnoredQueryParams(): Collection
    {
        return $this->ignoredQueryParams;
    }

    public function getHttpsRedirectCode(): int|null
    {
        return $this->httpsRedirectCode;
    }

    public function hasStreamingPlaylistBypass(): bool
    {
        return $this->streamingPlaylistBypass;
    }

    public function getCaching(): ResourceCaching
    {
        return $this->caching;
    }

    public function setCaching(ResourceCaching $caching): void
    {
        $this->caching = $caching;
    }

    public function getUpstreamFailTimeout(): int|null
    {
        return $this->upstreamFailTimeout;
    }

    public function setUpstreamFailTimeout(int|null $upstreamFailTimeout): void
    {
        $this->upstreamFailTimeout = $upstreamFailTimeout;
    }

    public function getUpstreamNextAttempts(): int|null
    {
        return $this->upstreamNextAttempts;
    }

    public function setUpstreamNextAttempts(int|null $upstreamNextAttempts): void
    {
        $this->upstreamNextAttempts = $upstreamNextAttempts;
    }

    /** @return mixed[]|null */
    public function getCustomData(): array|null
    {
        return $this->customData;
    }

    /** @param mixed[]|null $customData */
    public function setCustomData(array|null $customData): void
    {
        if ($customData === null) {
            $this->customData = null;
        } else {
            $this->customData = count($customData) === 0 ? null : $customData;
        }
    }

    public function hasQuic(): bool
    {
        return $this->quic;
    }

    public function hasWaf(): bool
    {
        return $this->waf;
    }

    public function hasCorsOriginHeader(): bool
    {
        return $this->corsOriginHeader;
    }

    public function hasCorsTimingEnabled(): bool
    {
        return $this->corsTimingEnabled;
    }

    public function hasCorsWildcardEnabled(): bool
    {
        return $this->corsWildcardEnabled;
    }

    public function modifyResource(EditedResource $editedResource): void
    {
        $this->corsOriginHeader = $editedResource->corsOriginHeader ?? $this->corsOriginHeader;
        $this->corsTimingEnabled = $editedResource->corsTimingEnabled ?? $this->corsTimingEnabled;
        $this->corsWildcardEnabled = $editedResource->corsWildcardEnabled ?? $this->corsWildcardEnabled;
        $this->disableQueryString = $editedResource->disableQueryString ?? $this->disableQueryString;
        $this->httpsRedirectCode = $editedResource->httpsRedirectCode === null
            ? $this->httpsRedirectCode
            : ($editedResource->httpsRedirectCode === 0 ? null : $editedResource->httpsRedirectCode);
        $this->ignoreSetCookie = $editedResource->ignoreSetCookie ?? $this->ignoreSetCookie;
        $this->instantSsl = $editedResource->instantSsl ?? $this->instantSsl;
        $this->mp4PseudoStreaming = $editedResource->mp4PseudoStreaming ?? $this->mp4PseudoStreaming;
        $this->quic = $editedResource->quic ?? $this->quic;
        $this->streamingPlaylistBypass = $editedResource->streamingPlaylistBypass ?? $this->streamingPlaylistBypass;
        $this->waf = $editedResource->waf ?? $this->waf;
        $this->rateLimit = $editedResource->rateLimit ?? $this->rateLimit;
        $this->contentDispositionByParam
            = $editedResource->contentDispositionByParam ?? $this->contentDispositionByParam;
    }

    public function hasRateLimit(): bool
    {
        return $this->rateLimit;
    }

    public function hasContentDispositionByParam(): bool
    {
        return $this->contentDispositionByParam;
    }

    public function getResourceSecureToken(): SecureToken|null
    {
        return $this->resourceSecureToken;
    }

    public function setResourceSecureToken(SecureToken|null $resourceSecureToken): void
    {
        $this->resourceSecureToken = $resourceSecureToken;
    }

    public function getResponseHeaders(): ResponseHeaders
    {
        return ResponseHeaders::fromDbValue($this->responseHeaders);
    }

    public function setResponseHeaders(ResponseHeaders|null $responseHeaders): void
    {
        $this->responseHeaders = ValueReplacer::emptyArrayToNull($responseHeaders?->toArray());
    }

    public function isInactive(): bool
    {
        return $this->isDeleted()
            || ($this->isSuspended() && $this->getSuspended() < new DateTimeImmutable('-30 days'));
    }

    public function isActive(): bool
    {
        return ! $this->isInactive();
    }

    private function buildSharedCnamesPattern(): string
    {
        return '~(?:'
            . implode(
                '|',
                array_map(
                    static fn (string $suffix): string => preg_quote($suffix, '~'),
                    self::CNAME_URL_SUFFIXES,
                ),
            )
            . ')\z~';
    }

    /** @return string[] */
    private function getSharedOrCustomCnames(bool $shared): array
    {
        static $suffixPattern;
        if ($suffixPattern === null) {
            $suffixPattern = $this->buildSharedCnamesPattern();
        }

        return array_values(
            array_filter(
                $this->cnames,
                static fn (string $cname): bool => ! $shared xor (bool) preg_match($suffixPattern, $cname),
            ),
        );
    }
}

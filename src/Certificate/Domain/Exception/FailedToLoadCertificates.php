<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class FailedToLoadCertificates extends DomainException implements NxgApiDomainException
{
    public static function accountDirNotFound(string $accountDir): self
    {
        return new self(sprintf('Account directory "%s" not found when loading certificates.', $accountDir));
    }

    public static function uuidDirNotFound(string $accountDir, string $uuid): self
    {
        return new self(sprintf('Certificate with UUID "%s" not found for account "%s".', $uuid, $accountDir));
    }

    public static function failedToReadFile(string $certificateFile): self
    {
        return new self(sprintf('Failed to read file "%s" when loading certificates.', $certificateFile));
    }
}

<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class FailedToStoreCertificatePair extends DomainException implements NxgApiDomainException
{
    public static function uuidDirAlreadyExists(string $uuidDir): self
    {
        return new self(sprintf('UUID directory "%s" already exists when storing certificate pair.', $uuidDir));
    }

    public static function failedToCreateAccountDir(string $accountDir): self
    {
        return new self(
            sprintf('Failed to create account directory "%s" when storing certificate pair.', $accountDir),
        );
    }

    public static function failedToDeleteUuidDir(string $uuidDir): self
    {
        return new self(sprintf('Failed to delete old UUID directory "%s" when storing certificate pair.', $uuidDir));
    }

    public static function failedToCreateUuidDir(string $uuidDir): self
    {
        return new self(sprintf('Failed to create UUID directory "%s" when storing certificate pair.', $uuidDir));
    }

    public static function failedToWriteCertificatePair(string $uuidDir): self
    {
        return new self(sprintf('Failed to write certificate pair to UUID directory "%s".', $uuidDir));
    }
}

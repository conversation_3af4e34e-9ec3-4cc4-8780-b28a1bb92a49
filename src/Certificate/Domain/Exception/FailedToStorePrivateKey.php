<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class FailedToStorePrivateKey extends DomainException implements NxgApiDomainException
{
    public static function failedToCreateAccountDir(string $accountDir): self
    {
        return new self(
            sprintf('Failed to create account directory "%s" when storing private key.', $accountDir),
        );
    }

    public static function failedToWritePrivateKey(string $accountDir): self
    {
        return new self(sprintf('Failed to write private key to directory "%s".', $accountDir));
    }
}

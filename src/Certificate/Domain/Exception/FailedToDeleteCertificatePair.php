<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class FailedToDeleteCertificatePair extends DomainException implements NxgApiDomainException
{
    public static function failedToDeleteUuidDir(string $uuidDir): self
    {
        return new self(sprintf('Failed to delete UUID directory "%s" when deleting certificate.', $uuidDir));
    }

    public static function failedToDeleteAccountDir(string $accountDir): self
    {
        return new self(sprintf('Failed to delete account directory "%s" when deleting certificate.', $accountDir));
    }

    public static function uuidDirNotFound(string $uuidDir): self
    {
        return new self(sprintf('UUID directory "%s" not found when deleting certificate.', $uuidDir));
    }

    public static function accountDirNotFound(string $accountDir): self
    {
        return new self(sprintf('Account directory "%s" not found when deleting certificate.', $accountDir));
    }
}

<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class FailedToDeleteCertificatePair extends DomainException implements NxgApiDomainException
{
    public static function failedToDeleteUuidDir(string $path): self
    {
        return new self(sprintf('Failed to delete file "%s" when deleting private key.', $path));
    }

    public static function failedToDeleteAccountDir(string $accountDir): self
    {
        return new self(sprintf('Failed to delete account directory "%s" when deleting private key.', $accountDir));
    }

    public static function fileNotFound(string $path): self
    {
        return new self(sprintf('File "%s" not found when deleting private key.', $path));
    }

    public static function accountDirNotFound(string $accountDir): self
    {
        return new self(sprintf('Account directory "%s" not found when deleting private key.', $accountDir));
    }
}

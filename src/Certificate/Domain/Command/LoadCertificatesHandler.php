<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToLoadCertificates;
use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\StorageAttributes;
use Throwable;

use function basename;
use function Sentry\captureException;
use function sprintf;

final class LoadCertificatesHandler implements QueryHandler
{
    public function __construct(
        private readonly FilesystemOperator $certificateStorage,
    ) {
    }

    /** @return array<string, array{key: string}> */
    public function handle(LoadCertificates $command): array
    {
        try {
            $accountDirName = (string) $command->accountId;

            // Check if account directory exists
            if (! $this->certificateStorage->directoryExists($accountDirName)) {
                throw FailedToLoadCertificates::accountDirNotFound($accountDirName);
            }

            return $this->loadKeysForAccount($accountDirName, $command->uuid);
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    /** @return array<string, array{key: string}> */
    private function loadKeysForAccount(string $accountDirName, string|null $uuid = null): array
    {
        if ($uuid !== null && ! $this->keyFileExists($accountDirName, $uuid)) {
            throw FailedToLoadCertificates::uuidDirNotFound($accountDirName, $uuid);
        }

        $keyFiles = $this->certificateStorage->listContents($accountDirName)
            ->filter(static fn (StorageAttributes $attributes) => $attributes->isFile() && str_ends_with($attributes->path(), '.key'))
            ->toArray();

        $keys = [];

        foreach ($keyFiles as $keyFile) {
            $fileName = basename($keyFile->path());
            $uuidFromFile = str_replace('.key', '', $fileName);

            if ($uuid !== null && $uuidFromFile !== $uuid) {
                continue;
            }

            $keys[$uuidFromFile] = [
                'key' => $this->readKeyFile($keyFile->path()),
            ];
        }

        return $keys;
    }

    /** @throws FailedToLoadCertificates */
    private function readKeyFile(string $keyFilePath): string
    {
        try {
            return $this->certificateStorage->read($keyFilePath);
        } catch (Throwable) {
            throw FailedToLoadCertificates::failedToReadFile($keyFilePath);
        }
    }

    private function keyFileExists(string $accountDirName, string $uuid): bool
    {
        $keyFilePath = sprintf('%s/%s.key', $accountDirName, $uuid);
        return $this->certificateStorage->fileExists($keyFilePath);
    }
}

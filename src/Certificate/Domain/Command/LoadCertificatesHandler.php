<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToLoadCertificates;
use Cdn77\NxgApi\Core\Domain\Bus\QueryHandler;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\StorageAttributes;
use Throwable;

use function basename;
use function Sentry\captureException;
use function sprintf;

final class LoadCertificatesHandler implements QueryHandler
{
    public function __construct(
        private readonly FilesystemOperator $certificateStorage,
    ) {
    }

    /** @return array<string, array{certificate: string, key: string}> */
    public function handle(LoadCertificates $command): array
    {
        try {
            $accountDirName = (string) $command->accountId;

            // Check if account directory exists
            if (! $this->certificateStorage->directoryExists($accountDirName)) {
                throw FailedToLoadCertificates::accountDirNotFound($accountDirName);
            }

            return $this->loadCertificatesForAccount($accountDirName, $command->uuid);
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    /** @return array<string, array{certificate: string, key: string}> */
    private function loadCertificatesForAccount(string $accountDirName, string|null $uuid = null): array
    {
        if ($uuid !== null && ! $this->certificateStorage->directoryExists($accountDirName . '/' . $uuid)) {
            throw FailedToLoadCertificates::uuidDirNotFound($accountDirName, $uuid);
        }

        $uuidDirectories = $this->certificateStorage->listContents($accountDirName)
            ->filter(static fn (StorageAttributes $attributes) => $attributes->isDir())
            ->toArray();

        $pairs = [];

        foreach ($uuidDirectories as $uuidDir) {
            $uuidDirName = basename($uuidDir->path());

            if ($uuid !== null && $uuidDirName !== $uuid) {
                continue;
            }

            $pairs[$uuidDirName] = [
                'certificate' => $this->readFile($uuidDir->path(), StoreCertificateHandler::CERTIFICATE_FILE),
                'key' => $this->readFile($uuidDir->path(), StoreCertificateHandler::PRIVATE_KEY_FILE),
            ];
        }

        return $pairs;
    }

    /** @throws FailedToLoadCertificates */
    private function readFile(string $uuidDirPath, string $fileName): string
    {
        $filePath = sprintf('%s/%s', $uuidDirPath, $fileName);

        try {
            return $this->certificateStorage->read($filePath);
        } catch (Throwable) {
            throw FailedToLoadCertificates::failedToReadFile($filePath);
        }
    }
}

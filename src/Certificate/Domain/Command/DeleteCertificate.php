<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Application\Payload\DeleteCertificateSchema;
use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Webmozart\Assert\Assert;

final class DeleteCertificate implements Command
{
    private function __construct(
        public readonly int $accountId,
        public readonly string $uuid,
    ) {
    }

    public static function fromSchema(DeleteCertificateSchema $schema): self
    {
        Assert::notNull($schema->accountId);
        Assert::notNull($schema->uuid);

        return new self(
            $schema->accountId,
            $schema->uuid,
        );
    }

    public static function create(int $accountId, string $uuid): self
    {
        return new self(
            $accountId,
            $uuid,
        );
    }
}

<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Application\Payload\LoadCertificatesSchema;
use Cdn77\NxgApi\Core\Domain\Bus\Query;
use Webmozart\Assert\Assert;

final class LoadCertificates implements Query
{
    private function __construct(
        public readonly int $accountId,
        public readonly string|null $uuid = null,
    ) {
    }

    public static function fromSchema(LoadCertificatesSchema $schema): self
    {
        Assert::notNull($schema->accountId);

        return new self(
            $schema->accountId,
            $schema->uuid,
        );
    }
}

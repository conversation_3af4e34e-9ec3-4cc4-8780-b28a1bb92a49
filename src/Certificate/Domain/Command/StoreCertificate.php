<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Application\Payload\StoreCertificatesSchema;
use Cdn77\NxgApi\Core\Domain\Bus\Command;
use Webmozart\Assert\Assert;

final class StoreCertificate implements Command
{
    private function __construct(
        public readonly int $accountId,
        public readonly string $uuid,
        public readonly string $certificate,
        public readonly string $key,
    ) {
    }

    public static function fromSchema(StoreCertificatesSchema $schema): self
    {
        Assert::notNull($schema->accountId);
        Assert::notNull($schema->uuid);
        Assert::notNull($schema->certificate);
        Assert::notNull($schema->key);

        return new self(
            $schema->accountId,
            $schema->uuid,
            $schema->certificate,
            $schema->key,
        );
    }
}

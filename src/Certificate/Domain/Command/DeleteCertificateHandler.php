<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToDeleteCertificatePair;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use League\Flysystem\FilesystemOperator;
use Throwable;

use function count;
use function Sentry\captureException;

final class DeleteCertificateHandler implements CommandHandler
{
    public function __construct(
        private readonly FilesystemOperator $certificateStorage,
    ) {
    }

    public function handle(DeleteCertificate $command): void
    {
        try {
            $keyFilePath = $this->getKeyFilePath($command->accountId, $command->uuid);

            $this->deleteKeyFile($keyFilePath);

            $this->deleteAccountDirIfEmpty($command->accountId);
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    private function deleteKeyFile(string $keyFilePath): void
    {
        try {
            $this->certificateStorage->delete($keyFilePath);
        } catch (Throwable) {
            throw FailedToDeleteCertificatePair::failedToDeleteUuidDir($keyFilePath);
        }
    }

    private function getKeyFilePath(string $accountDirName, string $uuid): string
    {
        $keyFilePath = StoreCertificateHandler::createKeyFileName($accountDirName, $uuid);

        if (! $this->certificateStorage->directoryExists($accountDirName)) {
            throw FailedToDeleteCertificatePair::accountDirNotFound($accountDirName);
        }

        if (! $this->certificateStorage->fileExists($keyFilePath)) {
            throw FailedToDeleteCertificatePair::fileNotFound($keyFilePath);
        }

        return $keyFilePath;
    }

    private function deleteAccountDirIfEmpty(string $accountDirName): void
    {
        $remainingKeyFiles = $this->certificateStorage->listContents($accountDirName)
            ->filter(static fn ($attr)
                => $attr->isFile() && str_ends_with($attr->path(), StoreCertificateHandler::KEY_FILE_EXTENSION)
            )
            ->toArray();

        if (count($remainingKeyFiles) !== 0) {
            return;
        }

        try {
            $this->certificateStorage->deleteDirectory($accountDirName);
        } catch (Throwable) {
            captureException(FailedToDeleteCertificatePair::failedToDeleteAccountDir($accountDirName));
        }
    }
}

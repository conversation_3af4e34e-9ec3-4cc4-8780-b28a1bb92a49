<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToDeleteCertificatePair;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use League\Flysystem\FilesystemOperator;
use Throwable;

use function count;
use function Sentry\captureException;

final class DeleteCertificateHandler implements CommandHandler
{
    public function __construct(
        private readonly FilesystemOperator $certificateStorage,
    ) {
    }

    public function handle(DeleteCertificate $command): void
    {
        try {
            [$accountDirName, $uuidDirName] = $this->validateDirectoryExists($command);

            try {
                $this->certificateStorage->deleteDirectory($uuidDirName);
            } catch (Throwable) {
                throw FailedToDeleteCertificatePair::failedToDeleteUuidDir($uuidDirName);
            }

            $this->deleteAccountDirIfEmpty($accountDirName);
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    /** @return array{string, string} */
    private function validateDirectoryExists(DeleteCertificate $command): array
    {
        $accountDirName = (string) $command->accountId;
        $uuidDirName = $accountDirName . '/' . $command->uuid;

        if (! $this->certificateStorage->directoryExists($accountDirName)) {
            throw FailedToDeleteCertificatePair::accountDirNotFound($accountDirName);
        }

        if (! $this->certificateStorage->directoryExists($uuidDirName)) {
            throw FailedToDeleteCertificatePair::uuidDirNotFound($uuidDirName);
        }

        return [$accountDirName, $uuidDirName];
    }

    private function deleteAccountDirIfEmpty(string $accountDirName): void
    {
        $otherCertificates = $this->certificateStorage->listContents($accountDirName)->toArray();

        if (count($otherCertificates) !== 0) {
            return;
        }

        try {
            $this->certificateStorage->deleteDirectory($accountDirName);
        } catch (Throwable) {
            throw FailedToDeleteCertificatePair::failedToDeleteAccountDir($accountDirName);
        }
    }
}

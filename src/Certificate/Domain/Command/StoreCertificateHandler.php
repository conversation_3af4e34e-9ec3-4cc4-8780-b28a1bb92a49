<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToStorePrivateKey;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificatePairValidator;
use League\Flysystem\FilesystemOperator;
use Throwable;

use function Sentry\captureException;
use function sprintf;

final class StoreCertificateHandler implements CommandHandler
{
    public const string KEY_FILE_EXTENSION = '.key';

    public function __construct(
        private readonly CertificatePairValidator $certificatePairValidator,
        private readonly FilesystemOperator $certificateStorage,
    ) {
    }

    public function handle(StoreCertificate $command): void
    {
        try {
            $this->certificatePairValidator->validate(new CertificatePair($command->certificate, $command->key));

            $this->prepareAccountDir($command->accountId);

            $this->writeKeyFile($command->accountId, $command->uuid, $command->key);
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    private function writeKeyFile(string $accountDirName, string $uuid, string $privateKey): void
    {
        $keyFileName = self::createKeyFileName($accountDirName, $uuid);

        try {
            $this->certificateStorage->write($keyFileName, $privateKey);
        } catch (Throwable) {
            throw FailedToStorePrivateKey::failedToWritePrivateKey($keyFileName);
        }
    }

    private function prepareAccountDir(string $accountDir): void
    {
        if (! $this->certificateStorage->directoryExists($accountDir)) {
            try {
                $this->certificateStorage->createDirectory($accountDir);
            } catch (Throwable) {
                throw FailedToStorePrivateKey::failedToCreateAccountDir($accountDir);
            }
        }
    }

    public static function createKeyFileName(string $accountDir, string $uuid): string
    {
        return sprintf('%s/%s%s', $accountDir, $uuid, self::KEY_FILE_EXTENSION);
    }
}

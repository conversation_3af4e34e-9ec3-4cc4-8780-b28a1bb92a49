<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Domain\Command;

use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToStoreCertificatePair;
use Cdn77\NxgApi\Core\Domain\Bus\CommandHandler;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificatePairValidator;
use League\Flysystem\FilesystemOperator;
use Throwable;

use function Sentry\captureException;
use function sprintf;

final class StoreCertificateHandler implements CommandHandler
{
    public const string CERTIFICATE_FILE = 'certificate.pem';
    public const string PRIVATE_KEY_FILE = 'private.key';

    public function __construct(
        private readonly CertificatePairValidator $certificatePairValidator,
        private readonly FilesystemOperator $certificateStorage,
    ) {
    }

    public function handle(StoreCertificate $command): void
    {
        try {
            $certificatePair = new CertificatePair($command->certificate, $command->key);
            $this->certificatePairValidator->validate($certificatePair);

            $accountDirName = $this->prepareAccountDir($command->accountId);
            $uuidDirName = $this->prepareUuidDir($accountDirName, $command->uuid);

            $this->writeFiles($uuidDirName, $certificatePair);
        } catch (Throwable $e) {
            captureException($e);

            throw $e;
        }
    }

    private function writeFiles(string $uuidDirName, CertificatePair $certificatePair): void
    {
        try {
            $this->certificateStorage->write(
                sprintf('%s/%s', $uuidDirName, self::CERTIFICATE_FILE),
                $certificatePair->getCertificate(),
            );
            $this->certificateStorage->write(
                sprintf('%s/%s', $uuidDirName, self::PRIVATE_KEY_FILE),
                $certificatePair->getPrivateKey(),
            );
        } catch (Throwable) {
            $this->certificateStorage->deleteDirectory($uuidDirName);

            throw FailedToStoreCertificatePair::failedToWriteCertificatePair($uuidDirName);
        }
    }

    private function prepareAccountDir(int $accountId): string
    {
        $accountDir = (string) $accountId;

        if (! $this->certificateStorage->directoryExists($accountDir)) {
            try {
                $this->certificateStorage->createDirectory($accountDir);
            } catch (Throwable) {
                throw FailedToStoreCertificatePair::failedToCreateAccountDir($accountDir);
            }
        }

        return $accountDir;
    }

    private function prepareUuidDir(string $accountDir, string $uuid): string
    {
        $uuidDir = $accountDir . '/' . $uuid;

        try {
            $this->certificateStorage->deleteDirectory($uuidDir);
        } catch (Throwable) {
            throw FailedToStoreCertificatePair::failedToDeleteUuidDir($uuidDir);
        }

        try {
            $this->certificateStorage->createDirectory($uuidDir);
        } catch (Throwable) {
            throw FailedToStoreCertificatePair::failedToCreateUuidDir($uuidDir);
        }

        return $uuidDir;
    }
}

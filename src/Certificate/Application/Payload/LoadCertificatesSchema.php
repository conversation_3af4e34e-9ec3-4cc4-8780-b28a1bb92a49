<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\Schema;
use Symfony\Component\Validator\Constraints as Assert;

final class LoadCertificatesSchema implements Schema
{
    public const FIELD_ACCOUNT_ID = 'account_id';
    public const FIELD_UUID = 'uuid';

    /**
     * @Assert\NotNull
     * @Assert\Type("integer")
     * @Assert\GreaterThan(0)
     */
    public int|null $accountId = null;

    /**
     * @Assert\Type("string")
     * @Assert\Uuid(versions={4}, message="UUID must be a valid version 4 UUID")
     */
    public string|null $uuid = null;

    private function __construct()
    {
    }
}

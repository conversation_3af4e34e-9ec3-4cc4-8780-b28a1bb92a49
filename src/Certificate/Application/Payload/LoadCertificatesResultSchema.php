<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use JMS\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

final class LoadCertificatesResultSchema implements QueryBusResultSchema
{
    /**
     * @Serializer\Type("array<string, array>")
     * @Serializer\Inline
     * @var array<string, array{certificate: string, key: string}>
     */
    public array $certificates;

    /** @param array<string, array{certificate: string, key: string}> $certificates */
    private function __construct(array $certificates)
    {
        $this->certificates = $certificates;
    }

    /** @param mixed $result */
    public static function fromQueryBusResult($result): self
    {
        Assert::isArray($result);

        return new self($result);
    }
}

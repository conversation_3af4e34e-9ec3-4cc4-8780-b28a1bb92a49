<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Payload;

use Cdn77\NxgApi\Core\Application\Payload\QueryBusResultSchema;
use JMS\Serializer\Annotation as Serializer;
use Webmozart\Assert\Assert;

final class LoadCertificatesResultSchema implements QueryBusResultSchema
{
    /**
     * @Serializer\Type("array<string, array>")
     * @Serializer\Inline
     * @var array<string, array{key: string}>
     */
    public array $keys;

    /** @param array<string, array{key: string}> $keys */
    private function __construct(array $keys)
    {
        $this->keys = $keys;
    }

    /** @param mixed $result */
    public static function fromQueryBusResult($result): self
    {
        Assert::isArray($result);

        return new self($result);
    }
}

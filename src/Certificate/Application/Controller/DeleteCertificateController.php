<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\DeleteCertificateSchema;
use Cdn77\NxgApi\Certificate\Domain\Command\DeleteCertificate;
use Cdn77\NxgApi\Certificate\Domain\Exception\FailedToDeleteCertificatePair;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Throwable;

final class DeleteCertificateController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'certificates.delete';
    private const ROUTE_SUMMARY = 'Delete a certificate from storage for an account by UUID';

    public function __construct(
        private readonly CommandBus $commandBus,
        private readonly ControllerSchemaSerializer $controllerSchemaSerializer,
        private readonly PathGenerator $pathGenerator,
    ) {
    }

    /**
     * @Route(
     *     path="/certificate",
     *     methods={Request::METHOD_DELETE},
     *     name=DeleteCertificateController::ROUTE_NAME
     * )
     */
    public function execute(Request $request): Response
    {
        $schema = $this->controllerSchemaSerializer->deserializeQueryString($request, DeleteCertificateSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        try {
            $this->commandBus->handle(DeleteCertificate::fromSchema($schema));
        } catch (FailedToDeleteCertificatePair $e) {
            return new JsonResponse(
                ['error' => $e->getMessage()],
                Response::HTTP_NOT_FOUND,
            );
        } catch (Throwable) {
            return new JsonResponse(
                ['error' => 'Failed to delete certificate.'],
                Response::HTTP_INTERNAL_SERVER_ERROR,
            );
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $delete = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                [
                    'name' => DeleteCertificateSchema::FIELD_ACCOUNT_ID,
                    'in' => 'query',
                    'description' => 'Account ID',
                    'required' => true,
                    'schema' => [
                        'type' => 'integer',
                        'minimum' => 1,
                    ],
                ],
                [
                    'name' => DeleteCertificateSchema::FIELD_UUID,
                    'in' => 'query',
                    'description' => 'Certificate UUID',
                    'required' => true,
                    'schema' => ['type' => 'string'],
                ],
            ],
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'Certificate deleted successfully'],
                ),
                Response::HTTP_BAD_REQUEST => new \cebe\openapi\spec\Response(
                    ['description' => 'Server error'],
                ),
                Response::HTTP_NOT_FOUND => new \cebe\openapi\spec\Response(
                    ['description' => 'Certificate not found'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['delete' => $delete])];
    }
}

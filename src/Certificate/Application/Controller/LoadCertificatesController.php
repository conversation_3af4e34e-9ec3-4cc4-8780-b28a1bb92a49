<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\LoadCertificatesResultSchema;
use Cdn77\NxgApi\Certificate\Application\Payload\LoadCertificatesSchema;
use Cdn77\NxgApi\Certificate\Domain\Command\LoadCertificates;
use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Application\Response\ControllerQueryHandler;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Response;
use cebe\openapi\spec\Responses;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

final class LoadCertificatesController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'certificates.load';
    private const ROUTE_SUMMARY = 'Load all certificates or a specific certificate for an account';

    public function __construct(
        private readonly ControllerQueryHandler $controllerQueryHandler,
        private readonly ControllerSchemaSerializer $controllerSchemaSerializer,
        private readonly PathGenerator $pathGenerator,
    ) {
    }

    /** @Route(path="/certificate", name=self::ROUTE_NAME, methods={Request::METHOD_GET}) */
    public function execute(Request $request): JsonResponse
    {
        $schema = $this->controllerSchemaSerializer->deserializeQueryString($request, LoadCertificatesSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        return $this->controllerQueryHandler->handle(
            LoadCertificates::fromSchema($schema),
            LoadCertificatesResultSchema::class,
            JsonResponse::HTTP_OK,
            true,
        );
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $get = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'parameters' => [
                [
                    'name' => LoadCertificatesSchema::FIELD_ACCOUNT_ID,
                    'in' => 'query',
                    'required' => true,
                    'schema' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'minimum' => 1,
                    ],
                    'description' => 'Account ID to load certificates for',
                ],
                [
                    'name' => LoadCertificatesSchema::FIELD_UUID,
                    'in' => 'query',
                    'required' => false,
                    'schema' => ['type' => 'string'],
                    'description' => 'Optional UUID to load a specific certificate pair',
                ],
            ],
            'responses' => new Responses([
                JsonResponse::HTTP_OK => new Response([
                    'description' => 'Certificates loaded successfully',
                    'content' => [
                        'application/json' => [
                            'schema' => [
                                'type' => 'object',
                                'additionalProperties' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'certificate' => ['type' => 'string'],
                                        'key' => ['type' => 'string'],
                                    ],
                                    'required' => ['certificate', 'key'],
                                ],
                            ],
                        ],
                    ],
                ]),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['get' => $get])];
    }
}

parameters:
    level: 8
    parallel:
        maximumNumberOfProcesses: 4
    tmpDir: %currentWorkingDirectory%/var/phpstan
    excludePaths:
        - %currentWorkingDirectory%/src/Server/Application/Controller/ServerIdListController.php
    paths:
        - %currentWorkingDirectory%/bin/console
        - %currentWorkingDirectory%/public
        - %currentWorkingDirectory%/src
        - %currentWorkingDirectory%/tests
    doctrine:
        objectManagerLoader: tests/doctrine-orm-object-manager-bootstrap.php
    ignoreErrors:
        # Safe\DateTimeImmutable - would create an overhead
        - '~Class DateTime(Immutable)? is unsafe to use. Its methods can return FALSE instead of throwing an exception.~'
        # Doctrine Collection bug - fixed in https://github.com/doctrine/collections/pull/323]
        - '#Class .+ extends generic class Symfony\\Component\\Validator\\Test\\ConstraintValidatorTestCase but does not specify its types: T#'
    errorFormat: ticketswap
    editorUrl: 'phpstorm://open?file=%%file%%&line=%%line%%'

includes:
    - phpstan-baseline.neon
    - phpstan-baseline.tests.neon
    - phpstan-baseline.doctrine.neon

<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate;

use Cdn77\NxgApi\Certificate\Domain\Command\StoreCertificateHandler;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use League\Flysystem\FilesystemOperator;
use Ramsey\Uuid\Uuid;

use function assert;
use function basename;
use function sprintf;

trait CertificateStorageTemporaryData
{
    private FilesystemOperator $certificateStorage;

    protected function setUpCertificateStorage(): void
    {
        $filesystem = self::$container->get('oneup_flysystem.certificate_storage_filesystem');
        assert($filesystem instanceof FilesystemOperator);
        $this->certificateStorage = $filesystem;
        $this->cleanCertificateStorage();
    }

    protected function cleanCertificateStorage(): void
    {
        $this->certificateStorage->deleteDirectory('');
    }

    /** @return array<string, CertificatePair> */
    protected function prepareMultipleCertificates(int $accountId, int $count = 3): array
    {
        $this->certificateStorage->createDirectory((string) $accountId);

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePairs = [];

        for ($i = 0; $i < $count; $i++) {
            $uuid = Uuid::uuid4()->toString();
            $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();
            $certificatePairs[$uuid] = $certificatePair;

            $this->storeCertificate($accountId, $uuid, $certificatePair);
        }

        return $certificatePairs;
    }

    protected function storeCertificate(int $accountId, string $uuid, CertificatePair $certificatePair): void
    {
        $uuidDir = sprintf('%s/%s', $accountId, $uuid);

        $this->certificateStorage->createDirectory($uuidDir);

        $this->certificateStorage->write(
            $uuidDir . '/' . StoreCertificateHandler::CERTIFICATE_FILE,
            $certificatePair->getCertificate(),
        );

        $this->certificateStorage->write(
            $uuidDir . '/' . StoreCertificateHandler::PRIVATE_KEY_FILE,
            $certificatePair->getPrivateKey(),
        );
    }

    protected function createAccountDirectory(int $accountId): void
    {
        $this->certificateStorage->createDirectory((string) $accountId);
    }

    protected function accountDirectoryExists(int $accountId): bool
    {
        return $this->certificateStorage->directoryExists((string) $accountId);
    }

    protected function certificateDirectoryExists(int $accountId, string $uuid): bool
    {
        return $this->certificateStorage->directoryExists(sprintf('%s/%s', $accountId, $uuid));
    }

    /** @return list<string> */
    protected function getAccountDirectoryContents(int $accountId): array
    {
        if (! $this->accountDirectoryExists($accountId)) {
            return [];
        }

        return $this->certificateStorage->listContents((string) $accountId)
            ->filter(static fn ($attributes) => $attributes->isDir())
            ->map(static fn ($attributes) => basename($attributes->path()))
            ->toArray();
    }

    protected function generateRandomCertificatePair(): CertificatePair
    {
        return (new CertificatePairGenerator())->generateRandomCertificatePair();
    }

    protected function getStoredCertificateContent(int $accountId, string $uuid): string
    {
        $certificateFile = sprintf('%s/%s/%s', $accountId, $uuid, StoreCertificateHandler::CERTIFICATE_FILE);

        return $this->certificateStorage->read($certificateFile);
    }

    protected function getStoredPrivateKeyContent(int $accountId, string $uuid): string
    {
        $privateKeyFile = sprintf('%s/%s/%s', $accountId, $uuid, StoreCertificateHandler::PRIVATE_KEY_FILE);

        return $this->certificateStorage->read($privateKeyFile);
    }

    protected function verifyStoredCertificateContent(int $accountId, string $uuid, CertificatePair $expectedPair): void
    {
        $storedCertificate = $this->getStoredCertificateContent($accountId, $uuid);
        $storedPrivateKey = $this->getStoredPrivateKeyContent($accountId, $uuid);

        self::assertSame($storedCertificate, $expectedPair->getCertificate());
        self::assertSame($storedPrivateKey, $expectedPair->getPrivateKey());
    }
}

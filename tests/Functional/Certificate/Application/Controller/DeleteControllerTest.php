<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\DeleteCertificateSchema;
use Cdn77\NxgApi\Tests\Functional\Certificate\CertificateStorageTemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\LetsEncrypt\LetsEncryptHelper;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\LetsEncrypt\LetsEncryptTemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function http_build_query;
use function sprintf;

final class DeleteControllerTest extends WebTestCase
{
    use EntityGetter;
    use TemporaryData;
    use LetsEncryptHelper;
    use LetsEncryptTemporaryData;
    use CertificateStorageTemporaryData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpCertificateStorage();
    }

    public function testDeleteLastPair(): void
    {
        $accountId = 12345;
        $certificatePairs = $this->prepareMultipleCertificates($accountId, 1);

        $response = $this->makeDeleteRequest($accountId, array_keys($certificatePairs)[0]);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertFalse($this->accountDirectoryExists($accountId));
    }

    public function testDeleteOnePairFromMultiple(): void
    {
        $accountId = 12345;
        $certificatePairs = $this->prepareMultipleCertificates($accountId);

        $uuidToDelete = array_keys($certificatePairs)[1];

        $response = $this->makeDeleteRequest($accountId, $uuidToDelete);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertTrue($this->accountDirectoryExists($accountId));

        self::assertCount(2, $this->getAccountDirectoryContents($accountId));
        self::assertNotContains($uuidToDelete, $this->getAccountDirectoryContents($accountId));
    }

    public function testDeleteWithInvalidAccountId(): void
    {
        $response = $this->makeDeleteRequest(0, Uuid::uuid4()->toString());

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    public function testDeleteWithInvalidUuid(): void
    {
        $accountId = 12345;
        $this->prepareMultipleCertificates($accountId);
        $randomUuid = Uuid::uuid4()->toString();
        $response = $this->makeDeleteRequest(12345, $randomUuid);

        self::assertSame(Response::HTTP_NOT_FOUND, $response->statusCode);
        self::assertSame(
            [
                'error' => sprintf(
                    'UUID directory "%s/%s" not found when deleting certificate.',
                    $accountId,
                    $randomUuid,
                ),
            ],
            $response->decodedContent,
        );
    }

    /** @return Generator<string, array{string}> */
    public static function providerInvalidUuids(): Generator
    {
        yield 'empty string' => [''];
        yield 'invalid format' => ['not-a-valid-uuid'];
        yield 'uuid v1' => ['550e8400-e29b-11d4-a716-************'];
        yield 'uuid v3' => ['6ba7b810-9dad-31d1-80b4-00c04fd430c8'];
        yield 'uuid v5' => ['6ba7b811-9dad-51d1-80b4-00c04fd430c8'];
        yield 'invalid characters' => ['550e8400-e29b-11d4-a716-44665544000g'];
    }

    /** @dataProvider providerInvalidUuids */
    public function testDeleteWithInvalidUuidFormat(string $invalidUuid): void
    {
        $response = $this->makeDeleteRequest(12345, $invalidUuid);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    private function makeDeleteRequest(int $accountId, string $uuid): ResponseDecoded
    {
        $queryString = http_build_query([
            DeleteCertificateSchema::FIELD_ACCOUNT_ID => $accountId,
            DeleteCertificateSchema::FIELD_UUID => $uuid,
        ]);

        $this->client->request(
            HttpRequest::METHOD_DELETE,
            '/certificate?' . $queryString,
            [],
            [],
            static::getDefaultHeaders(),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}

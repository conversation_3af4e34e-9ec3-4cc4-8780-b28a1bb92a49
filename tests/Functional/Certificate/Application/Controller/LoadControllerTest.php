<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\LoadCertificatesSchema;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Tests\Functional\Certificate\CertificateStorageTemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\LetsEncrypt\LetsEncryptHelper;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\LetsEncrypt\LetsEncryptTemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function sprintf;

final class LoadControllerTest extends WebTestCase
{
    use EntityGetter;
    use TemporaryData;
    use LetsEncryptHelper;
    use LetsEncryptTemporaryData;
    use CertificateStorageTemporaryData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpCertificateStorage();
    }

    public function testLoadWhenAccountDirMissing(): void
    {
        $accountId = 12345;

        $response = $this->makeRequest($accountId);

        self::assertSame(Response::HTTP_NOT_FOUND, $response->statusCode);
        self::assertEquals(
            ['errors' => ['Account directory "12345" not found when loading certificates.']],
            $response->decodedContent,
        );
    }

    public function testLoadWhenAccountUuidDirMissing(): void
    {
        $accountId = 12345;
        $uuid = Uuid::uuid4();

        $this->createAccountDirectory($accountId);

        $response = $this->makeRequest($accountId, $uuid->toString());

        self::assertSame(Response::HTTP_NOT_FOUND, $response->statusCode);
        self::assertEquals(
            ['errors' => [sprintf('Certificate with UUID "%s" not found for account "%s".', $uuid, $accountId)]],
            $response->decodedContent,
        );
    }

    public function testLoadForMultipleStoredCertificates(): void
    {
        $accountId = 12345;
        $certificatePairs = $this->prepareMultipleCertificates($accountId);

        $response = $this->makeRequest($accountId);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertCount(3, $response->decodedContent);

        foreach (array_keys($certificatePairs) as $uuid) {
            $this->validatePair($response, $uuid, $certificatePairs[$uuid]);
        }
    }

    public function testLoadForSpecificCertificate(): void
    {
        $accountId = 12345;
        $certificatePairs = $this->prepareMultipleCertificates($accountId);

        $specificUuid = array_keys($certificatePairs)[1];
        $response = $this->makeRequest($accountId, $specificUuid);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        self::assertCount(1, $response->decodedContent);

        $this->validatePair($response, $specificUuid, $certificatePairs[$specificUuid]);
    }

    private function makeRequest(int $accountId, string|null $uuid = null): ResponseDecoded
    {
        $queryParams = [LoadCertificatesSchema::FIELD_ACCOUNT_ID => $accountId];

        if ($uuid !== null) {
            $queryParams[LoadCertificatesSchema::FIELD_UUID] = $uuid;
        }

        $this->client->request(
            HttpRequest::METHOD_GET,
            '/certificate',
            $queryParams,
            [],
            static::getDefaultHeaders(),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }

    private function validatePair(ResponseDecoded $response, string $uuid, CertificatePair $certificatePair): void
    {
        self::assertArrayHasKey($uuid, $response->decodedContent);
        self::assertIsArray($response->decodedContent[$uuid]);

        /** @var array<string, mixed> $certificateData */
        $certificateData = $response->decodedContent[$uuid];

        self::assertArrayHasKey('certificate', $certificateData);
        self::assertArrayHasKey('key', $certificateData);
        self::assertEquals($certificatePair->getCertificate(), $certificateData['certificate']);
        self::assertEquals($certificatePair->getPrivateKey(), $certificateData['key']);
    }

    /** @return Generator<string, array{string}> */
    public static function providerInvalidUuids(): Generator
    {
        yield 'invalid format' => ['not-a-valid-uuid'];
        yield 'uuid v1' => ['550e8400-e29b-11d4-a716-************'];
        yield 'uuid v3' => ['6ba7b810-9dad-31d1-80b4-00c04fd430c8'];
        yield 'uuid v5' => ['6ba7b811-9dad-51d1-80b4-00c04fd430c8'];
        yield 'invalid characters' => ['550e8400-e29b-11d4-a716-44665544000g'];
    }

    /** @dataProvider providerInvalidUuids */
    public function testLoadWithInvalidUuid(string $invalidUuid): void
    {
        $accountId = 12345;
        $this->prepareMultipleCertificates($accountId);

        $response = $this->makeRequest($accountId, $invalidUuid);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }
}

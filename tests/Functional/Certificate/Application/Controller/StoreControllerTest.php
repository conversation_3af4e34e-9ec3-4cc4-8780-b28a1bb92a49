<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Certificate\Application\Controller;

use Cdn77\NxgApi\Certificate\Application\Payload\StoreCertificatesSchema;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Tests\Functional\Certificate\CertificateStorageTemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\Controller\LetsEncrypt\LetsEncryptHelper;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\LetsEncrypt\LetsEncryptTemporaryData;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request as HttpRequest;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function Safe\json_encode;

final class StoreControllerTest extends WebTestCase
{
    use EntityGetter;
    use TemporaryData;
    use LetsEncryptHelper;
    use LetsEncryptTemporaryData;
    use CertificateStorageTemporaryData;

    protected function setUp(): void
    {
        parent::setUp();

        $this->setUpCertificateStorage();
    }

    public function testAddCertificateToExistingAccount(): void
    {
        $accountId = 12345;
        $uuid = Uuid::uuid4()->toString();

        $existingCertificatePairs = $this->prepareMultipleCertificates($accountId, 1);
        $existingUuid = array_keys($existingCertificatePairs)[0];

        self::assertTrue($this->accountDirectoryExists($accountId));
        self::assertTrue($this->certificateDirectoryExists($accountId, $existingUuid));

        $newCertificatePair = $this->generateRandomCertificatePair();
        $response = $this->makeRequest($accountId, $uuid, $newCertificatePair);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertEmpty($response->response->getContent());

        self::assertTrue($this->certificateDirectoryExists($accountId, $uuid));
        self::assertTrue($this->certificateDirectoryExists($accountId, $existingUuid));

        $certificates = $this->getAccountDirectoryContents($accountId);
        self::assertCount(2, $certificates);
        self::assertContains($existingUuid, $certificates);
        self::assertContains($uuid, $certificates);

        $this->verifyStoredCertificateContent($accountId, $uuid, $newCertificatePair);
    }

    public function testAddCertificateToNewAccount(): void
    {
        $accountId = 12345;
        $uuid = Uuid::uuid4()->toString();

        self::assertFalse($this->accountDirectoryExists($accountId));

        $certificatePair = $this->generateRandomCertificatePair();

        $response = $this->makeRequest($accountId, $uuid, $certificatePair);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertEmpty($response->response->getContent());

        self::assertTrue($this->accountDirectoryExists($accountId));
        self::assertTrue($this->certificateDirectoryExists($accountId, $uuid));

        $certificates = $this->getAccountDirectoryContents($accountId);
        self::assertCount(1, $certificates);
        self::assertContains($uuid, $certificates);

        $this->verifyStoredCertificateContent($accountId, $uuid, $certificatePair);
    }

    public function testRewriteCertificateWithSameUuid(): void
    {
        $accountId = 12345;
        $uuid = Uuid::uuid4()->toString();

        $initialCertificatePair = $this->generateRandomCertificatePair();
        $this->storeCertificate($accountId, $uuid, $initialCertificatePair);
        self::assertTrue($this->certificateDirectoryExists($accountId, $uuid));

        $newCertificatePair = $this->generateRandomCertificatePair();
        $response = $this->makeRequest($accountId, $uuid, $newCertificatePair);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->statusCode);
        self::assertEmpty($response->response->getContent());

        self::assertTrue($this->certificateDirectoryExists($accountId, $uuid));

        $certificates = $this->getAccountDirectoryContents($accountId);
        self::assertCount(1, $certificates);
        self::assertContains($uuid, $certificates);

        $this->verifyStoredCertificateContent($accountId, $uuid, $newCertificatePair);
    }

    public function testFailAddInvalidCertificatePair(): void
    {
        $accountId = 12345;
        $uuid = Uuid::uuid4()->toString();

        $validCertificatePair1 = $this->generateRandomCertificatePair();
        $validCertificatePair2 = $this->generateRandomCertificatePair();

        // Mix certificate from pair1 with key from pair2 (invalid combination)
        $invalidCertificate = $validCertificatePair1->getCertificate();
        $invalidKey = $validCertificatePair2->getPrivateKey();

        $response = $this->makeRequestRaw($accountId, $uuid, $invalidCertificate, $invalidKey);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
        self::assertNotEmpty($response->decodedContent['errors']);
        self::assertContains(
            'Certificate and private key must form a valid certificate pair.',
            $response->decodedContent['errors'],
        );

        self::assertFalse($this->accountDirectoryExists($accountId));
        self::assertFalse($this->certificateDirectoryExists($accountId, $uuid));
    }

    public function testStoreWithInvalidAccountId(): void
    {
        $response = $this->makeRequestRaw(0, Uuid::uuid4()->toString(), 'cert', 'key');

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    /** @return Generator<string, array{string}> */
    public static function providerInvalidUuids(): Generator
    {
        yield 'empty string' => [''];
        yield 'invalid format' => ['not-a-valid-uuid'];
        yield 'uuid v1' => ['550e8400-e29b-11d4-a716-************'];
        yield 'uuid v3' => ['6ba7b810-9dad-31d1-80b4-00c04fd430c8'];
        yield 'uuid v5' => ['6ba7b811-9dad-51d1-80b4-00c04fd430c8'];
        yield 'invalid characters' => ['550e8400-e29b-11d4-a716-44665544000g'];
    }

    /** @dataProvider providerInvalidUuids */
    public function testStoreWithInvalidUuid(string $invalidUuid): void
    {
        $certificatePair = $this->generateRandomCertificatePair();

        $response = $this->makeRequestRaw(
            12345,
            $invalidUuid,
            $certificatePair->getCertificate(),
            $certificatePair->getPrivateKey(),
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    public function testStoreWithEmptyCertificate(): void
    {
        $certificatePair = $this->generateRandomCertificatePair();

        $response = $this->makeRequestRaw(
            12345,
            Uuid::uuid4()->toString(),
            '',
            $certificatePair->getPrivateKey(),
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    public function testStoreWithEmptyKey(): void
    {
        $certificatePair = $this->generateRandomCertificatePair();

        $response = $this->makeRequestRaw(
            12345,
            Uuid::uuid4()->toString(),
            $certificatePair->getCertificate(),
            '',
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertArrayHasKey('errors', $response->decodedContent);
    }

    private function makeRequest(int $accountId, string $uuid, CertificatePair $certificatePair): ResponseDecoded
    {
        return $this->makeRequestRaw(
            $accountId,
            $uuid,
            $certificatePair->getCertificate(),
            $certificatePair->getPrivateKey(),
        );
    }

    private function makeRequestRaw(int $accountId, string $uuid, string $certificate, string $key): ResponseDecoded
    {
        $requestData = [
            StoreCertificatesSchema::FIELD_ACCOUNT_ID => $accountId,
            StoreCertificatesSchema::FIELD_UUID => $uuid,
            StoreCertificatesSchema::FIELD_CERTIFICATE => $certificate,
            StoreCertificatesSchema::FIELD_PRIVATE_KEY => $key,
        ];

        $this->client->request(
            HttpRequest::METHOD_POST,
            '/certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode($requestData),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
